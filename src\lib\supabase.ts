import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

export type Database = {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          full_name: string | null
          avatar_url: string | null
          onboarding_completed: boolean
          writing_goals: string[]
          experience: string
          genres: string[]
          daily_goal: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          avatar_url?: string | null
          onboarding_completed?: boolean
          writing_goals?: string[]
          experience?: string
          genres?: string[]
          daily_goal?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          onboarding_completed?: boolean
          writing_goals?: string[]
          experience?: string
          genres?: string[]
          daily_goal?: number
          created_at?: string
          updated_at?: string
        }
      }
      books: {
        Row: {
          id: string
          user_id: string
          title: string
          author: string
          word_count: number
          settings: any
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          title: string
          author: string
          word_count?: number
          settings?: any
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          title?: string
          author?: string
          word_count?: number
          settings?: any
          created_at?: string
          updated_at?: string
        }
      }
      chapters: {
        Row: {
          id: string
          book_id: string
          title: string
          content: string
          word_count: number
          order_index: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          book_id: string
          title: string
          content?: string
          word_count?: number
          order_index: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          book_id?: string
          title?: string
          content?: string
          word_count?: number
          order_index?: number
          created_at?: string
          updated_at?: string
        }
      }
      scenes: {
        Row: {
          id: string
          chapter_id: string
          title: string
          content: string
          word_count: number
          order_index: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          chapter_id: string
          title: string
          content?: string
          word_count?: number
          order_index: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          chapter_id?: string
          title?: string
          content?: string
          word_count?: number
          order_index?: number
          created_at?: string
          updated_at?: string
        }
      }
      characters: {
        Row: {
          id: string
          book_id: string
          name: string
          description: string | null
          role: string | null
          appearance: string | null
          personality: string | null
          backstory: string | null
          goals: string | null
          notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          book_id: string
          name: string
          description?: string | null
          role?: string | null
          appearance?: string | null
          personality?: string | null
          backstory?: string | null
          goals?: string | null
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          book_id?: string
          name?: string
          description?: string | null
          role?: string | null
          appearance?: string | null
          personality?: string | null
          backstory?: string | null
          goals?: string | null
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      plot_points: {
        Row: {
          id: string
          book_id: string
          title: string
          description: string | null
          chapter_id: string | null
          scene_id: string | null
          completed: boolean
          order_index: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          book_id: string
          title: string
          description?: string | null
          chapter_id?: string | null
          scene_id?: string | null
          completed?: boolean
          order_index: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          book_id?: string
          title?: string
          description?: string | null
          chapter_id?: string | null
          scene_id?: string | null
          completed?: boolean
          order_index?: number
          created_at?: string
          updated_at?: string
        }
      }
    }
  }
}