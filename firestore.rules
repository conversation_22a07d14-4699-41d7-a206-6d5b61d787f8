rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only access their own profile
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Users can only access their own books
    match /books/{bookId} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
      allow create: if request.auth != null && request.auth.uid == request.resource.data.userId;
      
      // Chapters subcollection
      match /chapters/{chapterId} {
        allow read, write: if request.auth != null && request.auth.uid == get(/databases/$(database)/documents/books/$(bookId)).data.userId;
        
        // Scenes subcollection
        match /scenes/{sceneId} {
          allow read, write: if request.auth != null && request.auth.uid == get(/databases/$(database)/documents/books/$(bookId)).data.userId;
        }
      }
      
      // Characters subcollection
      match /characters/{characterId} {
        allow read, write: if request.auth != null && request.auth.uid == get(/databases/$(database)/documents/books/$(bookId)).data.userId;
      }
      
      // Plot points subcollection
      match /plotPoints/{plotPointId} {
        allow read, write: if request.auth != null && request.auth.uid == get(/databases/$(database)/documents/books/$(bookId)).data.userId;
      }
    }
  }
}
