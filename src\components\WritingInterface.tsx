import React, { useState, useEffect } from 'react';
import { Book, Chapter, Scene, Character } from '../types';
import { User } from '@supabase/supabase-js';
import { 
  ChevronDown, 
  ChevronRight, 
  Plus, 
  X, 
  ArrowRight, 
  Focus, 
  Edit3, 
  Layers3, 
  Eye,
  Trash2,
  Edit,
  Settings,
  FileText,
  Users,
  BookOpen,
  MoreHorizontal,
  LogOut
} from 'lucide-react';
import { 
  createNewChapter, 
  createNewScene, 
  updateBookWordCount, 
  countWords,
  deleteChapter,
  deleteScene
} from '../utils/bookUtils';
import { RichTextEditor } from './RichTextEditor';
import { WritingToolsModal } from './WritingToolsModal';
import { ProjectElementsModal } from './ProjectElementsModal';

interface WritingInterfaceProps {
  book: Book;
  books: Book[];
  onUpdateBook: (book: Book) => void;
  onCreateBook: (title: string, author: string) => void;
  onSelectBook: (book: Book) => void;
  onDeleteBook: (bookId: string) => void;
  onSignOut: () => void;
  user: User;
  backgroundImage?: string;
}

export const WritingInterface: React.FC<WritingInterfaceProps> = ({ 
  book, 
  books,
  onUpdateBook,
  onCreateBook,
  onSelectBook,
  onDeleteBook,
  onSignOut,
  user,
  backgroundImage = 'https://www.outdoorpainter.com/wp-content/uploads/2019/03/painting-clouds-Kim-Casebeer-Sunset-at-The-Clark-8x10-1024x821.jpg'
}) => {
  const [selectedChapter, setSelectedChapter] = useState<Chapter | null>(null);
  const [selectedScene, setSelectedScene] = useState<Scene | null>(null);
  const [expandedChapters, setExpandedChapters] = useState<Set<string>>(new Set());
  const [showNewChapterInput, setShowNewChapterInput] = useState(false);
  const [showNewSceneInput, setShowNewSceneInput] = useState<string | null>(null);
  const [showNewBookInput, setShowNewBookInput] = useState(false);
  const [newChapterTitle, setNewChapterTitle] = useState('');
  const [newSceneTitle, setNewSceneTitle] = useState('');
  const [newBookTitle, setNewBookTitle] = useState('');
  const [newBookAuthor, setNewBookAuthor] = useState('');
  const [focusMode, setFocusMode] = useState(false);
  const [editingChapter, setEditingChapter] = useState<string | null>(null);
  const [editingScene, setEditingScene] = useState<string | null>(null);
  const [editingBook, setEditingBook] = useState<string | null>(null);
  const [editChapterTitle, setEditChapterTitle] = useState('');
  const [editSceneTitle, setEditSceneTitle] = useState('');
  const [editBookTitle, setEditBookTitle] = useState('');
  const [editBookAuthor, setEditBookAuthor] = useState('');
  const [showWritingTools, setShowWritingTools] = useState(false);
  const [showProjectElements, setShowProjectElements] = useState(false);

  useEffect(() => {
    if (book.chapters.length > 0 && !selectedChapter) {
      const firstChapter = book.chapters[0];
      setSelectedChapter(firstChapter);
      setExpandedChapters(new Set([firstChapter.id]));
      
      if (firstChapter.scenes.length > 0) {
        setSelectedScene(firstChapter.scenes[0]);
      }
    }
  }, [book, selectedChapter]);

  const updateContent = (content: string) => {
    const wordCount = countWords(content);
    
    if (selectedScene && selectedChapter) {
      updateSceneContent(content, wordCount);
    }
  };

  const updateSceneContent = (content: string, wordCount: number) => {
    if (!selectedScene || !selectedChapter) return;

    const updatedScene = { ...selectedScene, content, wordCount, updatedAt: new Date().toISOString() };
    const updatedChapter = {
      ...selectedChapter,
      scenes: selectedChapter.scenes.map(scene => 
        scene.id === selectedScene.id ? updatedScene : scene
      ),
      updatedAt: new Date().toISOString()
    };
    
    const updatedBook = {
      ...book,
      chapters: book.chapters.map(chapter => 
        chapter.id === selectedChapter.id ? updatedChapter : chapter
      )
    };

    const finalBook = updateBookWordCount(updatedBook);
    onUpdateBook(finalBook);
    setSelectedScene(updatedScene);
    setSelectedChapter(updatedChapter);
  };

  const toggleChapterExpansion = (chapterId: string) => {
    const newExpanded = new Set(expandedChapters);
    if (newExpanded.has(chapterId)) {
      newExpanded.delete(chapterId);
    } else {
      newExpanded.add(chapterId);
    }
    setExpandedChapters(newExpanded);
  };

  const handleCreateChapter = () => {
    if (!newChapterTitle.trim()) return;

    const newChapter = createNewChapter(newChapterTitle.trim(), book.chapters.length);
    const updatedBook = {
      ...book,
      chapters: [...book.chapters, newChapter],
      updatedAt: new Date().toISOString()
    };

    onUpdateBook(updatedBook);
    setNewChapterTitle('');
    setShowNewChapterInput(false);
    setSelectedChapter(newChapter);
    setSelectedScene(null);
    setExpandedChapters(new Set([...expandedChapters, newChapter.id]));
  };

  const handleCreateScene = (chapterId: string) => {
    if (!newSceneTitle.trim()) return;

    const chapter = book.chapters.find(c => c.id === chapterId);
    if (!chapter) return;

    const newScene = createNewScene(newSceneTitle.trim(), chapter.scenes.length);
    const updatedChapter = {
      ...chapter,
      scenes: [...chapter.scenes, newScene],
      updatedAt: new Date().toISOString()
    };

    const updatedBook = {
      ...book,
      chapters: book.chapters.map(c => c.id === chapterId ? updatedChapter : c),
      updatedAt: new Date().toISOString()
    };

    onUpdateBook(updatedBook);
    setNewSceneTitle('');
    setShowNewSceneInput(null);
    setSelectedChapter(updatedChapter);
    setSelectedScene(newScene);
  };

  const handleCreateBook = () => {
    if (!newBookTitle.trim() || !newBookAuthor.trim()) return;

    onCreateBook(newBookTitle.trim(), newBookAuthor.trim());
    setNewBookTitle('');
    setNewBookAuthor('');
    setShowNewBookInput(false);
  };

  const handleDeleteChapter = (chapterId: string) => {
    if (window.confirm('Are you sure you want to delete this chapter? This action cannot be undone.')) {
      const updatedBook = deleteChapter(book, chapterId);
      onUpdateBook(updatedBook);
      
      if (selectedChapter?.id === chapterId) {
        setSelectedChapter(null);
        setSelectedScene(null);
      }
    }
  };

  const handleDeleteScene = (chapterId: string, sceneId: string) => {
    if (window.confirm('Are you sure you want to delete this scene? This action cannot be undone.')) {
      const updatedBook = deleteScene(book, chapterId, sceneId);
      onUpdateBook(updatedBook);
      
      if (selectedScene?.id === sceneId) {
        setSelectedScene(null);
      }
    }
  };

  const handleDeleteBook = (bookId: string) => {
    if (window.confirm('Are you sure you want to delete this book? This action cannot be undone.')) {
      onDeleteBook(bookId);
    }
  };

  const handleEditChapter = (chapter: Chapter) => {
    setEditingChapter(chapter.id);
    setEditChapterTitle(chapter.title);
  };

  const handleSaveChapterEdit = () => {
    if (!editingChapter || !editChapterTitle.trim()) return;

    const updatedBook = {
      ...book,
      chapters: book.chapters.map(chapter => 
        chapter.id === editingChapter 
          ? { ...chapter, title: editChapterTitle.trim(), updatedAt: new Date().toISOString() }
          : chapter
      ),
      updatedAt: new Date().toISOString()
    };

    onUpdateBook(updatedBook);
    setEditingChapter(null);
    setEditChapterTitle('');
  };

  const handleEditScene = (scene: Scene) => {
    setEditingScene(scene.id);
    setEditSceneTitle(scene.title);
  };

  const handleSaveSceneEdit = () => {
    if (!editingScene || !editSceneTitle.trim() || !selectedChapter) return;

    const updatedChapter = {
      ...selectedChapter,
      scenes: selectedChapter.scenes.map(scene => 
        scene.id === editingScene 
          ? { ...scene, title: editSceneTitle.trim(), updatedAt: new Date().toISOString() }
          : scene
      ),
      updatedAt: new Date().toISOString()
    };

    const updatedBook = {
      ...book,
      chapters: book.chapters.map(chapter => 
        chapter.id === selectedChapter.id ? updatedChapter : chapter
      ),
      updatedAt: new Date().toISOString()
    };

    onUpdateBook(updatedBook);
    setEditingScene(null);
    setEditSceneTitle('');
    setSelectedChapter(updatedChapter);
  };

  const handleEditBook = (bookToEdit: Book) => {
    setEditingBook(bookToEdit.id);
    setEditBookTitle(bookToEdit.title);
    setEditBookAuthor(bookToEdit.author);
  };

  const handleSaveBookEdit = () => {
    if (!editingBook || !editBookTitle.trim() || !editBookAuthor.trim()) return;

    const updatedBook = {
      ...book,
      title: editBookTitle.trim(),
      author: editBookAuthor.trim(),
      updatedAt: new Date().toISOString()
    };

    onUpdateBook(updatedBook);
    setEditingBook(null);
    setEditBookTitle('');
    setEditBookAuthor('');
  };

  const handleSaveCharacterFromAI = (character: { name: string; description: string }) => {
    const newCharacter = {
      id: Date.now().toString(36) + Math.random().toString(36).substr(2),
      name: character.name,
      description: character.description,
      role: '',
      notes: '',
      appearance: '',
      personality: '',
      backstory: '',
      goals: '',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    const updatedBook = {
      ...book,
      characters: [...book.characters, newCharacter],
      updatedAt: new Date().toISOString()
    };

    onUpdateBook(updatedBook);
  };

  const getCurrentLocation = () => {
    if (selectedScene && selectedChapter) {
      const chapterIndex = book.chapters.findIndex(c => c.id === selectedChapter.id);
      const sceneIndex = selectedChapter.scenes.findIndex(s => s.id === selectedScene.id);
      return `Chapter ${chapterIndex + 1}, Scene ${sceneIndex + 1}`;
    }
    return 'Select a scene to start writing';
  };

  const getCurrentContent = () => {
    if (selectedScene && selectedChapter) {
      // Find the current scene in the current chapter to get the most up-to-date content
      const currentChapter = book.chapters.find(c => c.id === selectedChapter.id);
      const currentScene = currentChapter?.scenes.find(s => s.id === selectedScene.id);
      return currentScene?.content || '';
    }
    return '';
  };

  if (focusMode) {
    return (
      <div 
        className="min-h-screen relative"
        style={{
          backgroundImage: `url(${backgroundImage})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundAttachment: 'fixed'
        }}
      >
        <div className="absolute inset-0 bg-black/20" />
        
        <div className="relative z-10 min-h-screen flex flex-col">
          {/* Header */}
          <div className="p-6">
            <div className="flex items-center justify-between">
              <h1 className="text-3xl font-serif font-semibold text-white">{book.title}</h1>
              <div className="flex items-center space-x-4 text-white/80 font-sans">
                <span>{getCurrentLocation()}</span>
                <div className="w-6 h-6 bg-white/20 rounded-full flex items-center justify-center">
                  <Eye className="w-4 h-4" />
                </div>
              </div>
            </div>
          </div>

          {/* Editor */}
          <div className="flex-1 px-6 pb-6">
            <div className="max-w-4xl mx-auto">
              <RichTextEditor
                content={getCurrentContent()}
                onChange={updateContent}
                placeholder="Start writing your story..."
                className="min-h-[600px]"
              />
            </div>
          </div>

          {/* Bottom Actions */}
          <div className="p-6">
            <div className="flex items-center justify-between max-w-4xl mx-auto">
              <button 
                onClick={() => setFocusMode(false)}
                className="flex items-center px-4 py-2 bg-white/10 backdrop-blur-md rounded-xl border border-white/20 text-white/80 hover:text-white transition-colors"
              >
                <Focus className="w-4 h-4 mr-2" />
                <span className="font-sans">Exit Focus Mode</span>
              </button>

              <button 
                onClick={() => setShowWritingTools(true)}
                className="flex items-center px-4 py-2 bg-white/10 backdrop-blur-md rounded-xl border border-white/20 text-white/80 hover:text-white transition-colors"
              >
                <Edit3 className="w-4 h-4 mr-2" />
                <span className="font-sans">Writing Tools</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div 
      className="min-h-screen relative"
      style={{
        backgroundImage: `url(${backgroundImage})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundAttachment: 'fixed'
      }}
    >
      <div className="absolute inset-0 bg-black/20" />
      
      <div className="relative z-10 flex h-screen">
        {/* Sidebar */}
        <div className="w-80 p-6 flex flex-col">
          <div className="bg-black/40 backdrop-blur-md rounded-2xl border border-white/10 flex-1 p-6 overflow-y-auto">
            <h1 className="text-2xl font-serif font-semibold text-white mb-6">WriterOne</h1>
            
            {/* Current Book */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-4">
                {editingBook === book.id ? (
                  <div className="flex-1 space-y-2">
                    <input
                      type="text"
                      value={editBookTitle}
                      onChange={(e) => setEditBookTitle(e.target.value)}
                      className="w-full px-2 py-1 bg-white/20 border border-white/30 rounded text-white placeholder-white/60 text-lg font-serif"
                      placeholder="Book title..."
                    />
                    <input
                      type="text"
                      value={editBookAuthor}
                      onChange={(e) => setEditBookAuthor(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && handleSaveBookEdit()}
                      onBlur={handleSaveBookEdit}
                      className="w-full px-2 py-1 bg-white/20 border border-white/30 rounded text-white placeholder-white/60 text-sm font-sans"
                      placeholder="Author name..."
                    />
                  </div>
                ) : (
                  <>
                    <div>
                      <h2 className="text-xl font-serif font-semibold text-white">{book.title}</h2>
                      <p className="text-white/60 text-sm font-sans">by {book.author}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button 
                        onClick={() => handleEditBook(book)}
                        className="text-white/60 hover:text-white"
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                      {books.length > 1 && (
                        <button 
                          onClick={() => handleDeleteBook(book.id)}
                          className="text-white/60 hover:text-red-400"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      )}
                    </div>
                  </>
                )}
              </div>
              <div className="text-white/60 text-sm font-sans">
                {book.wordCount.toLocaleString()} words • {book.chapters.length} chapters
              </div>
            </div>

            {/* Chapters */}
            <div className="space-y-2 mb-6">
              {book.chapters.map((chapter, chapterIndex) => (
                <div key={chapter.id}>
                  <div className="flex items-center justify-between group">
                    <button
                      onClick={() => {
                        toggleChapterExpansion(chapter.id);
                        setSelectedChapter(chapter);
                        // Don't allow writing at chapter level - must select a scene
                      }}
                      className={`flex-1 flex items-center justify-between p-3 rounded-lg text-left transition-colors ${
                        selectedChapter?.id === chapter.id
                          ? 'bg-white/10 text-white/90'
                          : 'text-white/80 hover:bg-white/5 hover:text-white'
                      }`}
                    >
                      {editingChapter === chapter.id ? (
                        <input
                          type="text"
                          value={editChapterTitle}
                          onChange={(e) => setEditChapterTitle(e.target.value)}
                          onKeyPress={(e) => e.key === 'Enter' && handleSaveChapterEdit()}
                          onBlur={handleSaveChapterEdit}
                          className="flex-1 px-2 py-1 bg-white/20 border border-white/30 rounded text-white placeholder-white/60 font-sans"
                          placeholder="Chapter title..."
                          autoFocus
                        />
                      ) : (
                        <span className="font-sans">{chapter.title}</span>
                      )}
                      {expandedChapters.has(chapter.id) ? (
                        <ChevronDown className="w-4 h-4" />
                      ) : (
                        <ChevronRight className="w-4 h-4" />
                      )}
                    </button>
                    <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                      <button 
                        onClick={() => handleEditChapter(chapter)}
                        className="text-white/60 hover:text-white p-1"
                      >
                        <Edit className="w-3 h-3" />
                      </button>
                      <button 
                        onClick={() => handleDeleteChapter(chapter.id)}
                        className="text-white/60 hover:text-red-400 p-1"
                      >
                        <Trash2 className="w-3 h-3" />
                      </button>
                    </div>
                  </div>

                  {expandedChapters.has(chapter.id) && (
                    <div className="ml-4 mt-2 space-y-1">
                      {chapter.scenes.map((scene, sceneIndex) => (
                        <div key={scene.id} className="flex items-center justify-between group">
                          <button
                            onClick={() => {
                              setSelectedChapter(chapter);
                              setSelectedScene(scene);
                            }}
                            className={`flex-1 flex items-center justify-between p-2 rounded-lg text-left transition-colors ${
                              selectedScene?.id === scene.id
                                ? 'bg-white/20 text-white'
                                : 'text-white/60 hover:bg-white/10 hover:text-white/80'
                            }`}
                          >
                            {editingScene === scene.id ? (
                              <input
                                type="text"
                                value={editSceneTitle}
                                onChange={(e) => setEditSceneTitle(e.target.value)}
                                onKeyPress={(e) => e.key === 'Enter' && handleSaveSceneEdit()}
                                onBlur={handleSaveSceneEdit}
                                className="flex-1 px-2 py-1 bg-white/20 border border-white/30 rounded text-white placeholder-white/60 text-sm font-sans"
                                placeholder="Scene title..."
                                autoFocus
                              />
                            ) : (
                              <span className="font-sans text-sm">{scene.title}</span>
                            )}
                            <ArrowRight className="w-3 h-3" />
                          </button>
                          <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                            <button 
                              onClick={() => handleEditScene(scene)}
                              className="text-white/60 hover:text-white p-1"
                            >
                              <Edit className="w-3 h-3" />
                            </button>
                            <button 
                              onClick={() => handleDeleteScene(chapter.id, scene.id)}
                              className="text-white/60 hover:text-red-400 p-1"
                            >
                              <Trash2 className="w-3 h-3" />
                            </button>
                          </div>
                        </div>
                      ))}

                      {showNewSceneInput === chapter.id ? (
                        <div className="p-2">
                          <input
                            type="text"
                            value={newSceneTitle}
                            onChange={(e) => setNewSceneTitle(e.target.value)}
                            onKeyPress={(e) => e.key === 'Enter' && handleCreateScene(chapter.id)}
                            onBlur={() => setShowNewSceneInput(null)}
                            className="w-full px-2 py-1 bg-white/20 border border-white/30 rounded text-white placeholder-white/60 text-sm font-sans"
                            placeholder="Scene title..."
                            autoFocus
                          />
                        </div>
                      ) : (
                        <button
                          onClick={() => setShowNewSceneInput(chapter.id)}
                          className="w-full flex items-center p-2 rounded-lg text-white/60 hover:bg-white/10 hover:text-white/80 transition-colors"
                        >
                          <Plus className="w-3 h-3 mr-2" />
                          <span className="font-sans text-sm">New Scene</span>
                        </button>
                      )}
                    </div>
                  )}
                </div>
              ))}

              {showNewChapterInput ? (
                <div className="p-3">
                  <input
                    type="text"
                    value={newChapterTitle}
                    onChange={(e) => setNewChapterTitle(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleCreateChapter()}
                    onBlur={() => setShowNewChapterInput(false)}
                    className="w-full px-3 py-2 bg-white/20 border border-white/30 rounded-lg text-white placeholder-white/60 font-sans"
                    placeholder="Chapter title..."
                    autoFocus
                  />
                </div>
              ) : (
                <button
                  onClick={() => setShowNewChapterInput(true)}
                  className="w-full flex items-center p-3 rounded-lg text-white/60 hover:bg-white/10 hover:text-white transition-colors"
                >
                  <Plus className="w-4 h-4 mr-3" />
                  <span className="font-sans">New Chapter</span>
                </button>
              )}
            </div>

            {/* Other Projects */}
            <div className="border-t border-white/20 pt-4">
              <h3 className="text-white/80 font-sans text-sm mb-3">Other Projects</h3>
              
              {/* User Info & Sign Out */}
              <div className="mb-4 p-3 bg-white/5 rounded-lg">
                <div className="flex items-center justify-between">
                  <div className="text-white/80 font-sans text-sm">{user.email}</div>
                  <button
                    onClick={onSignOut}
                    className="text-white/60 hover:text-white transition-colors"
                  >
                    <LogOut className="w-4 h-4" />
                  </button>
                </div>
              </div>
              
              {books.filter(b => b.id !== book.id).map((otherBook) => (
                <button 
                  key={otherBook.id}
                  onClick={() => onSelectBook(otherBook)}
                  className="w-full flex items-center justify-between p-3 rounded-lg text-white/60 hover:bg-white/10 hover:text-white transition-colors mb-2"
                >
                  <div className="text-left">
                    <div className="font-sans">{otherBook.title}</div>
                    <div className="text-xs text-white/40">by {otherBook.author}</div>
                  </div>
                  <ArrowRight className="w-4 h-4" />
                </button>
              ))}
              
              {showNewBookInput ? (
                <div className="p-3 space-y-2">
                  <input
                    type="text"
                    value={newBookTitle}
                    onChange={(e) => setNewBookTitle(e.target.value)}
                    className="w-full px-3 py-2 bg-white/20 border border-white/30 rounded-lg text-white placeholder-white/60 font-sans text-sm"
                    placeholder="Book title..."
                  />
                  <input
                    type="text"
                    value={newBookAuthor}
                    onChange={(e) => setNewBookAuthor(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleCreateBook()}
                    onBlur={() => setShowNewBookInput(false)}
                    className="w-full px-3 py-2 bg-white/20 border border-white/30 rounded-lg text-white placeholder-white/60 font-sans text-sm"
                    placeholder="Author name..."
                  />
                </div>
              ) : (
                <button
                  onClick={() => setShowNewBookInput(true)}
                  className="w-full flex items-center p-3 rounded-lg text-white/60 hover:bg-white/10 hover:text-white transition-colors"
                >
                  <Plus className="w-4 h-4 mr-3" />
                  <span className="font-sans">New Project</span>
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Main Editor */}
        <div className="flex-1 p-6 flex flex-col">
          {/* Header */}
          <div className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-6 mb-6">
            <div className="flex items-center justify-between">
              <h1 className="text-3xl font-serif font-semibold text-white">{book.title}</h1>
              <div className="flex items-center space-x-2 text-white/80 font-sans">
                <span>{getCurrentLocation()}</span>
                <div className="w-6 h-6 bg-white/20 rounded-full flex items-center justify-center">
                  <Eye className="w-4 h-4" />
                </div>
              </div>
            </div>
          </div>

          {/* Editor */}
          <div className="flex-1 mb-6 overflow-y-auto">
            {selectedScene ? (
              <RichTextEditor
                content={getCurrentContent()}
                onChange={updateContent}
                placeholder="Start writing your story..."
                className="h-full"
              />
            ) : (
              <div className="bg-white rounded-2xl shadow-2xl h-full flex items-center justify-center">
                <div className="text-center text-gray-500">
                  <FileText className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                  <h3 className="text-xl font-serif mb-2">Select a scene to start writing</h3>
                  <p className="font-sans">Choose a scene from the sidebar or create a new one to begin writing.</p>
                </div>
              </div>
            )}
          </div>

          {/* Bottom Actions */}
          <div className="flex items-center justify-between">
            <button 
              onClick={() => setFocusMode(true)}
              className="flex items-center px-4 py-2 bg-white/10 backdrop-blur-md rounded-xl border border-white/20 text-white/80 hover:text-white transition-colors"
            >
              <Focus className="w-4 h-4 mr-2" />
              <span className="font-sans">Focus Mode</span>
            </button>

            <button 
              onClick={() => setShowWritingTools(true)}
              className="flex items-center px-4 py-2 bg-white/10 backdrop-blur-md rounded-xl border border-white/20 text-white/80 hover:text-white transition-colors"
            >
              <Edit3 className="w-4 h-4 mr-2" />
              <span className="font-sans">Writing Tools</span>
            </button>

            <button 
              onClick={() => setShowProjectElements(true)}
              className="flex items-center px-4 py-2 bg-black/40 backdrop-blur-md rounded-xl border border-white/10 text-white hover:bg-black/50 transition-colors"
            >
              <Layers3 className="w-4 h-4 mr-2" />
              <span className="font-sans">Project Elements</span>
            </button>
          </div>
        </div>

        {/* Modals */}
        <WritingToolsModal
          isOpen={showWritingTools}
          onClose={() => setShowWritingTools(false)}
          currentContent={getCurrentContent()}
          onSaveCharacter={handleSaveCharacterFromAI}
        />

        <ProjectElementsModal
          isOpen={showProjectElements}
          onClose={() => setShowProjectElements(false)}
          book={book}
          onUpdateBook={onUpdateBook}
        />
      </div>
    </div>
  );
};