import React, { useState } from 'react'
import { ChevronRight, BookOpen, User, Target, Sparkles } from 'lucide-react'

interface OnboardingFlowProps {
  onComplete: (data: OnboardingData) => void
  userFullName?: string
}

export interface OnboardingData {
  writingGoals: string[]
  experience: string
  genres: string[]
  dailyGoal: number
  firstBookTitle: string
  firstBookAuthor: string
}

export const OnboardingFlow: React.FC<OnboardingFlowProps> = ({ onComplete, userFullName = '' }) => {
  const [currentStep, setCurrentStep] = useState(0)
  const [data, setData] = useState<OnboardingData>({
    writingGoals: [],
    experience: '',
    genres: [],
    dailyGoal: 500,
    firstBookTitle: '',
    firstBookAuthor: userFullName
  })

  const backgroundImage = 'https://images.pexels.com/photos/1366919/pexels-photo-1366919.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop'

  const steps = [
    {
      title: 'Welcome to WriterOne',
      subtitle: 'Let\'s set up your writing journey',
      icon: <Sparkles className="w-8 h-8" />
    },
    {
      title: 'What are your writing goals?',
      subtitle: 'Select all that apply',
      icon: <Target className="w-8 h-8" />
    },
    {
      title: 'What\'s your writing experience?',
      subtitle: 'This helps us personalize your experience',
      icon: <User className="w-8 h-8" />
    },
    {
      title: 'What genres interest you?',
      subtitle: 'Choose your favorite genres',
      icon: <BookOpen className="w-8 h-8" />
    },
    {
      title: 'Set your daily writing goal',
      subtitle: 'How many words do you want to write per day?',
      icon: <Target className="w-8 h-8" />
    },
    {
      title: 'Create your first book',
      subtitle: 'Let\'s start with your first project',
      icon: <BookOpen className="w-8 h-8" />
    }
  ]

  const writingGoalOptions = [
    'Complete a novel',
    'Write short stories',
    'Improve writing skills',
    'Write for fun',
    'Publish my work',
    'Join writing community'
  ]

  const experienceOptions = [
    { value: 'beginner', label: 'Beginner - Just starting out' },
    { value: 'intermediate', label: 'Intermediate - Some experience' },
    { value: 'advanced', label: 'Advanced - Experienced writer' },
    { value: 'professional', label: 'Professional - Published author' }
  ]

  const genreOptions = [
    'Fiction', 'Non-fiction', 'Fantasy', 'Science Fiction', 'Romance', 
    'Mystery', 'Thriller', 'Horror', 'Historical', 'Biography', 
    'Self-help', 'Poetry', 'Drama', 'Comedy'
  ]

  const handleGoalToggle = (goal: string) => {
    setData(prev => ({
      ...prev,
      writingGoals: prev.writingGoals.includes(goal)
        ? prev.writingGoals.filter(g => g !== goal)
        : [...prev.writingGoals, goal]
    }))
  }

  const handleGenreToggle = (genre: string) => {
    setData(prev => ({
      ...prev,
      genres: prev.genres.includes(genre)
        ? prev.genres.filter(g => g !== genre)
        : [...prev.genres, genre]
    }))
  }

  const canProceed = () => {
    switch (currentStep) {
      case 0: return true
      case 1: return data.writingGoals.length > 0
      case 2: return data.experience !== ''
      case 3: return data.genres.length > 0
      case 4: return data.dailyGoal > 0
      case 5: return data.firstBookTitle.trim() !== '' && data.firstBookAuthor.trim() !== ''
      default: return false
    }
  }

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1)
    } else {
      onComplete(data)
    }
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <div className="text-center space-y-6">
            <div className="w-24 h-24 bg-white/20 rounded-full flex items-center justify-center mx-auto">
              <BookOpen className="w-12 h-12 text-white" />
            </div>
            <div>
              <h2 className="text-2xl font-serif font-bold text-white mb-4">
                Welcome to WriterOne, {userFullName || 'Writer'}!
              </h2>
              <p className="text-white/80 font-sans leading-relaxed">
                We're excited to help you on your writing journey. Let's take a few moments to 
                personalize your experience and set you up for success.
              </p>
            </div>
          </div>
        )

      case 1:
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 gap-3">
              {writingGoalOptions.map((goal) => (
                <button
                  key={goal}
                  onClick={() => handleGoalToggle(goal)}
                  className={`p-4 rounded-xl border-2 transition-all text-left ${
                    data.writingGoals.includes(goal)
                      ? 'bg-white/20 border-white/50 text-white'
                      : 'bg-white/5 border-white/20 text-white/80 hover:bg-white/10'
                  }`}
                >
                  <span className="font-sans">{goal}</span>
                </button>
              ))}
            </div>
          </div>
        )

      case 2:
        return (
          <div className="space-y-4">
            {experienceOptions.map((option) => (
              <button
                key={option.value}
                onClick={() => setData(prev => ({ ...prev, experience: option.value }))}
                className={`w-full p-4 rounded-xl border-2 transition-all text-left ${
                  data.experience === option.value
                    ? 'bg-white/20 border-white/50 text-white'
                    : 'bg-white/5 border-white/20 text-white/80 hover:bg-white/10'
                }`}
              >
                <div className="font-sans font-semibold">{option.label.split(' - ')[0]}</div>
                <div className="font-sans text-sm text-white/70">{option.label.split(' - ')[1]}</div>
              </button>
            ))}
          </div>
        )

      case 3:
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-2 gap-3">
              {genreOptions.map((genre) => (
                <button
                  key={genre}
                  onClick={() => handleGenreToggle(genre)}
                  className={`p-3 rounded-xl border-2 transition-all ${
                    data.genres.includes(genre)
                      ? 'bg-white/20 border-white/50 text-white'
                      : 'bg-white/5 border-white/20 text-white/80 hover:bg-white/10'
                  }`}
                >
                  <span className="font-sans text-sm">{genre}</span>
                </button>
              ))}
            </div>
          </div>
        )

      case 4:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <div className="text-4xl font-serif font-bold text-white mb-2">
                {data.dailyGoal}
              </div>
              <div className="text-white/80 font-sans">words per day</div>
            </div>
            <input
              type="range"
              min="100"
              max="2000"
              step="50"
              value={data.dailyGoal}
              onChange={(e) => setData(prev => ({ ...prev, dailyGoal: parseInt(e.target.value) }))}
              className="w-full h-2 bg-white/20 rounded-lg appearance-none cursor-pointer slider"
            />
            <div className="flex justify-between text-white/60 font-sans text-sm">
              <span>100</span>
              <span>2000</span>
            </div>
          </div>
        )

      case 5:
        return (
          <div className="space-y-6">
            <div>
              <label className="block text-white/90 font-sans font-medium mb-2">
                Book Title
              </label>
              <input
                type="text"
                value={data.firstBookTitle}
                onChange={(e) => setData(prev => ({ ...prev, firstBookTitle: e.target.value }))}
                className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-xl text-white placeholder-white/60 font-sans focus:outline-none focus:ring-2 focus:ring-white/50"
                placeholder="Enter your book title"
              />
            </div>
            <div>
              <label className="block text-white/90 font-sans font-medium mb-2">
                Author Name
              </label>
              <input
                type="text"
                value={data.firstBookAuthor}
                onChange={(e) => setData(prev => ({ ...prev, firstBookAuthor: e.target.value }))}
                className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-xl text-white placeholder-white/60 font-sans focus:outline-none focus:ring-2 focus:ring-white/50"
                placeholder="Enter author name"
              />
            </div>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <div 
      className="min-h-screen flex items-center justify-center relative"
      style={{
        backgroundImage: `url(${backgroundImage})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundAttachment: 'fixed'
      }}
    >
      <div className="absolute inset-0 bg-black/30" />
      
      <div className="relative z-10 w-full max-w-2xl p-8">
        <div className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-8 shadow-2xl">
          {/* Progress */}
          <div className="mb-8">
            <div className="flex justify-between items-center mb-4">
              <span className="text-white/80 font-sans text-sm">
                Step {currentStep + 1} of {steps.length}
              </span>
              <span className="text-white/80 font-sans text-sm">
                {Math.round(((currentStep + 1) / steps.length) * 100)}%
              </span>
            </div>
            <div className="w-full bg-white/20 rounded-full h-2">
              <div 
                className="bg-white/60 h-2 rounded-full transition-all duration-300"
                style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
              />
            </div>
          </div>

          {/* Header */}
          <div className="text-center mb-8">
            <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4 text-white">
              {steps[currentStep].icon}
            </div>
            <h1 className="text-2xl font-serif font-bold text-white mb-2">
              {steps[currentStep].title}
            </h1>
            <p className="text-white/80 font-sans">
              {steps[currentStep].subtitle}
            </p>
          </div>

          {/* Content */}
          <div className="mb-8">
            {renderStepContent()}
          </div>

          {/* Navigation */}
          <div className="flex justify-between">
            <button
              onClick={() => setCurrentStep(Math.max(0, currentStep - 1))}
              disabled={currentStep === 0}
              className="px-6 py-3 bg-white/10 border border-white/20 rounded-xl text-white/80 font-sans font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed hover:bg-white/20"
            >
              Back
            </button>
            <button
              onClick={handleNext}
              disabled={!canProceed()}
              className="flex items-center px-6 py-3 bg-white/20 border border-white/30 rounded-xl text-white font-sans font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed hover:bg-white/30"
            >
              {currentStep === steps.length - 1 ? 'Complete Setup' : 'Continue'}
              <ChevronRight className="w-4 h-4 ml-2" />
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}