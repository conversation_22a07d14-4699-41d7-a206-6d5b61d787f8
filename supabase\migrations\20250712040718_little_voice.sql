/*
  # Create WriterOne Database Schema

  1. New Tables
    - `profiles` - User profile information and onboarding status
    - `books` - User's writing projects
    - `chapters` - Book chapters with ordering
    - `scenes` - Individual scenes within chapters  
    - `characters` - Character profiles and details
    - `plot_points` - Story plot tracking

  2. Security
    - Enable RLS on all tables
    - Add policies for authenticated users to access their own data
    - Users can only see/modify their own content

  3. Relationships
    - books.user_id -> auth.users.id
    - chapters.book_id -> books.id
    - scenes.chapter_id -> chapters.id
    - characters.book_id -> books.id
    - plot_points.book_id -> books.id
*/

-- Create profiles table
CREATE TABLE IF NOT EXISTS profiles (
  id uuid PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  email text NOT NULL,
  full_name text,
  avatar_url text,
  onboarding_completed boolean DEFAULT false,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create books table
CREATE TABLE IF NOT EXISTS books (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  title text NOT NULL,
  author text NOT NULL,
  word_count integer DEFAULT 0,
  settings jsonb DEFAULT '{"fontSize": 16, "fontFamily": "serif", "theme": "light", "lineHeight": 1.6, "paragraphSpacing": 1.2}',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create chapters table
CREATE TABLE IF NOT EXISTS chapters (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  book_id uuid NOT NULL REFERENCES books(id) ON DELETE CASCADE,
  title text NOT NULL,
  content text DEFAULT '',
  word_count integer DEFAULT 0,
  order_index integer NOT NULL,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create scenes table
CREATE TABLE IF NOT EXISTS scenes (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  chapter_id uuid NOT NULL REFERENCES chapters(id) ON DELETE CASCADE,
  title text NOT NULL,
  content text DEFAULT '',
  word_count integer DEFAULT 0,
  order_index integer NOT NULL,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create characters table
CREATE TABLE IF NOT EXISTS characters (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  book_id uuid NOT NULL REFERENCES books(id) ON DELETE CASCADE,
  name text NOT NULL,
  description text DEFAULT '',
  role text DEFAULT '',
  appearance text DEFAULT '',
  personality text DEFAULT '',
  backstory text DEFAULT '',
  goals text DEFAULT '',
  notes text DEFAULT '',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create plot_points table
CREATE TABLE IF NOT EXISTS plot_points (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  book_id uuid NOT NULL REFERENCES books(id) ON DELETE CASCADE,
  title text NOT NULL,
  description text DEFAULT '',
  chapter_id uuid REFERENCES chapters(id) ON DELETE SET NULL,
  scene_id uuid REFERENCES scenes(id) ON DELETE SET NULL,
  completed boolean DEFAULT false,
  order_index integer NOT NULL,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE books ENABLE ROW LEVEL SECURITY;
ALTER TABLE chapters ENABLE ROW LEVEL SECURITY;
ALTER TABLE scenes ENABLE ROW LEVEL SECURITY;
ALTER TABLE characters ENABLE ROW LEVEL SECURITY;
ALTER TABLE plot_points ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for profiles
CREATE POLICY "Users can read own profile"
  ON profiles
  FOR SELECT
  TO authenticated
  USING (auth.uid() = id);

CREATE POLICY "Users can update own profile"
  ON profiles
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile"
  ON profiles
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = id);

-- Create RLS policies for books
CREATE POLICY "Users can read own books"
  ON books
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own books"
  ON books
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own books"
  ON books
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own books"
  ON books
  FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);

-- Create RLS policies for chapters
CREATE POLICY "Users can read own chapters"
  ON chapters
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM books 
      WHERE books.id = chapters.book_id 
      AND books.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert own chapters"
  ON chapters
  FOR INSERT
  TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM books 
      WHERE books.id = chapters.book_id 
      AND books.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update own chapters"
  ON chapters
  FOR UPDATE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM books 
      WHERE books.id = chapters.book_id 
      AND books.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete own chapters"
  ON chapters
  FOR DELETE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM books 
      WHERE books.id = chapters.book_id 
      AND books.user_id = auth.uid()
    )
  );

-- Create RLS policies for scenes
CREATE POLICY "Users can read own scenes"
  ON scenes
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM chapters 
      JOIN books ON books.id = chapters.book_id
      WHERE chapters.id = scenes.chapter_id 
      AND books.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert own scenes"
  ON scenes
  FOR INSERT
  TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM chapters 
      JOIN books ON books.id = chapters.book_id
      WHERE chapters.id = scenes.chapter_id 
      AND books.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update own scenes"
  ON scenes
  FOR UPDATE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM chapters 
      JOIN books ON books.id = chapters.book_id
      WHERE chapters.id = scenes.chapter_id 
      AND books.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete own scenes"
  ON scenes
  FOR DELETE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM chapters 
      JOIN books ON books.id = chapters.book_id
      WHERE chapters.id = scenes.chapter_id 
      AND books.user_id = auth.uid()
    )
  );

-- Create RLS policies for characters
CREATE POLICY "Users can read own characters"
  ON characters
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM books 
      WHERE books.id = characters.book_id 
      AND books.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert own characters"
  ON characters
  FOR INSERT
  TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM books 
      WHERE books.id = characters.book_id 
      AND books.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update own characters"
  ON characters
  FOR UPDATE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM books 
      WHERE books.id = characters.book_id 
      AND books.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete own characters"
  ON characters
  FOR DELETE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM books 
      WHERE books.id = characters.book_id 
      AND books.user_id = auth.uid()
    )
  );

-- Create RLS policies for plot_points
CREATE POLICY "Users can read own plot_points"
  ON plot_points
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM books 
      WHERE books.id = plot_points.book_id 
      AND books.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert own plot_points"
  ON plot_points
  FOR INSERT
  TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM books 
      WHERE books.id = plot_points.book_id 
      AND books.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update own plot_points"
  ON plot_points
  FOR UPDATE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM books 
      WHERE books.id = plot_points.book_id 
      AND books.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete own plot_points"
  ON plot_points
  FOR DELETE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM books 
      WHERE books.id = plot_points.book_id 
      AND books.user_id = auth.uid()
    )
  );

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_books_user_id ON books(user_id);
CREATE INDEX IF NOT EXISTS idx_chapters_book_id ON chapters(book_id);
CREATE INDEX IF NOT EXISTS idx_scenes_chapter_id ON scenes(chapter_id);
CREATE INDEX IF NOT EXISTS idx_characters_book_id ON characters(book_id);
CREATE INDEX IF NOT EXISTS idx_plot_points_book_id ON plot_points(book_id);
CREATE INDEX IF NOT EXISTS idx_chapters_order ON chapters(book_id, order_index);
CREATE INDEX IF NOT EXISTS idx_scenes_order ON scenes(chapter_id, order_index);
CREATE INDEX IF NOT EXISTS idx_plot_points_order ON plot_points(book_id, order_index);

-- Create function to automatically create profile on signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
  INSERT INTO public.profiles (id, email, full_name)
  VALUES (new.id, new.email, new.raw_user_meta_data->>'full_name');
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically create profile on signup
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_trigger WHERE tgname = 'on_auth_user_created'
  ) THEN
    CREATE TRIGGER on_auth_user_created
      AFTER INSERT ON auth.users
      FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();
  END IF;
END $$;