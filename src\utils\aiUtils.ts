import { GoogleGenerativeAI } from '@google/generative-ai';

// Note: In a production app, this should be stored securely on the backend
// For demo purposes, we'll use a placeholder that users need to replace
const API_KEY = 'YOUR_GEMINI_API_KEY_HERE';

let genAI: GoogleGenerativeAI | null = null;

try {
  if (API_KEY && API_KEY !== 'YOUR_GEMINI_API_KEY_HERE') {
    genAI = new GoogleGenerativeAI(API_KEY);
  }
} catch (error) {
  console.warn('Gemini AI not configured. Please add your API key to use AI features.');
}

export const generateCharacterName = async (description: string): Promise<string> => {
  if (!genAI) {
    // Fallback to mock generation for demo
    const mockNames = [
      'Elena <PERSON>wood', 'Marcus Thornfield', 'Aria Moonwhisper', 'Darius Ironforge',
      'Luna Starweaver', 'Kai Shadowbane', 'Zara Nightfall', 'Orion Stormwind',
      'Sage Willowbrook', 'Phoenix Emberheart'
    ];
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    return mockNames[Math.floor(Math.random() * mockNames.length)];
  }

  try {
    const model = genAI.getGenerativeModel({ model: 'gemini-pro' });
    
    const prompt = `Generate a single, creative character name for a character described as: "${description}". 
    The name should be fitting for the character's description and suitable for a novel. 
    Return only the character name, nothing else.`;
    
    const result = await model.generateContent(prompt);
    const response = await result.response;
    const name = response.text().trim();
    
    return name;
  } catch (error) {
    console.error('Error generating character name:', error);
    throw new Error('Failed to generate character name');
  }
};

export const generateWritingPrompt = async (currentContent: string): Promise<string> => {
  if (!genAI) {
    // Fallback to mock prompts for demo
    const mockPrompts = [
      'What if your protagonist discovers a hidden message that changes everything they believed?',
      'Introduce a character from the past who holds the key to solving the current conflict.',
      'What secret is one of your characters hiding that could destroy their relationships?',
      'How might the weather or environment reflect your character\'s emotional state?',
      'What would happen if your character had to make an impossible choice between two things they value?',
      'Introduce an unexpected ally who comes from an unlikely place.',
      'What if your character discovers they\'ve been wrong about something fundamental?',
      'How could a seemingly minor detail from earlier become crucially important now?'
    ];
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    return mockPrompts[Math.floor(Math.random() * mockPrompts.length)];
  }

  try {
    const model = genAI.getGenerativeModel({ model: 'gemini-pro' });
    
    // Extract a sample of the content for analysis (last 500 characters)
    const contentSample = currentContent.slice(-500);
    
    const prompt = `Based on this excerpt from a story: "${contentSample}"
    
    Generate a single, creative writing prompt that would help the author continue their story. 
    The prompt should:
    - Be one sentence long
    - Suggest a specific direction or development
    - Be engaging and thought-provoking
    - Build naturally from what's already written
    
    Return only the writing prompt, nothing else.`;
    
    const result = await model.generateContent(prompt);
    const response = await result.response;
    const prompt_text = response.text().trim();
    
    return prompt_text;
  } catch (error) {
    console.error('Error generating writing prompt:', error);
    throw new Error('Failed to generate writing prompt');
  }
};