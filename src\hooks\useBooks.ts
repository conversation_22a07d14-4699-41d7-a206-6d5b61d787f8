import { useState, useEffect } from 'react'
import {
  collection,
  doc,
  getDocs,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  serverTimestamp,
  Timestamp
} from 'firebase/firestore'
import { db } from '../lib/firebase'
import { Book as FirebaseBook, Chapter as FirebaseChapter, Scene as FirebaseScene, Character as FirebaseCharacter, PlotPoint as FirebasePlotPoint, COLLECTIONS } from '../types/firebase'
import { Book, Chapter, Scene, Character, PlotPoint } from '../types'
import { countWords } from '../utils/bookUtils'

// Helper functions to convert between Firebase and app types
const convertTimestamp = (timestamp: Timestamp): string => {
  return timestamp.toDate().toISOString()
}

const convertFirebaseBook = (firebaseBook: FirebaseBook & { id: string }): Book => ({
  id: firebaseBook.id,
  title: firebaseBook.title,
  author: firebaseBook.author,
  createdAt: convertTimestamp(firebaseBook.createdAt),
  updatedAt: convertTimestamp(firebaseBook.updatedAt),
  wordCount: firebaseBook.wordCount,
  settings: firebaseBook.settings,
  chapters: [],
  characters: [],
  plotPoints: []
})

export function useBooks(userId: string | undefined) {
  const [books, setBooks] = useState<Book[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (userId) {
      fetchBooks()
    } else {
      setBooks([])
      setLoading(false)
    }
  }, [userId])

  const fetchBooks = async () => {
    if (!userId) return

    try {
      setLoading(true)

      // Fetch books for the user
      const booksQuery = query(
        collection(db, COLLECTIONS.BOOKS),
        where('userId', '==', userId),
        orderBy('createdAt', 'desc')
      )

      const booksSnapshot = await getDocs(booksQuery)
      const booksData: Book[] = []

      for (const bookDoc of booksSnapshot.docs) {
        const firebaseBook = { id: bookDoc.id, ...bookDoc.data() } as FirebaseBook & { id: string }
        const book = convertFirebaseBook(firebaseBook)

        // Fetch chapters for this book
        const chaptersQuery = query(
          collection(db, COLLECTIONS.BOOKS, book.id, 'chapters'),
          orderBy('orderIndex', 'asc')
        )
        const chaptersSnapshot = await getDocs(chaptersQuery)

        for (const chapterDoc of chaptersSnapshot.docs) {
          const firebaseChapter = { id: chapterDoc.id, ...chapterDoc.data() } as FirebaseChapter & { id: string }
          const chapter: Chapter = {
            id: firebaseChapter.id,
            bookId: firebaseChapter.bookId,
            title: firebaseChapter.title,
            content: firebaseChapter.content,
            wordCount: firebaseChapter.wordCount,
            order: firebaseChapter.orderIndex,
            orderIndex: firebaseChapter.orderIndex,
            createdAt: convertTimestamp(firebaseChapter.createdAt),
            updatedAt: convertTimestamp(firebaseChapter.updatedAt),
            scenes: []
          }

          // Fetch scenes for this chapter
          const scenesQuery = query(
            collection(db, COLLECTIONS.BOOKS, book.id, 'chapters', chapter.id, 'scenes'),
            orderBy('orderIndex', 'asc')
          )
          const scenesSnapshot = await getDocs(scenesQuery)

          chapter.scenes = scenesSnapshot.docs.map(sceneDoc => {
            const firebaseScene = { id: sceneDoc.id, ...sceneDoc.data() } as FirebaseScene & { id: string }
            return {
              id: firebaseScene.id,
              chapterId: firebaseScene.chapterId,
              title: firebaseScene.title,
              content: firebaseScene.content,
              wordCount: firebaseScene.wordCount,
              order: firebaseScene.orderIndex,
              orderIndex: firebaseScene.orderIndex,
              createdAt: convertTimestamp(firebaseScene.createdAt),
              updatedAt: convertTimestamp(firebaseScene.updatedAt)
            }
          })

          book.chapters.push(chapter)
        }

        // Fetch characters for this book
        const charactersQuery = collection(db, COLLECTIONS.BOOKS, book.id, 'characters')
        const charactersSnapshot = await getDocs(charactersQuery)

        book.characters = charactersSnapshot.docs.map(characterDoc => {
          const firebaseCharacter = { id: characterDoc.id, ...characterDoc.data() } as FirebaseCharacter & { id: string }
          return {
            id: firebaseCharacter.id,
            bookId: firebaseCharacter.bookId,
            name: firebaseCharacter.name,
            description: firebaseCharacter.description || '',
            role: firebaseCharacter.role || '',
            appearance: firebaseCharacter.appearance || '',
            personality: firebaseCharacter.personality || '',
            backstory: firebaseCharacter.backstory || '',
            goals: firebaseCharacter.goals || '',
            notes: firebaseCharacter.notes || '',
            createdAt: convertTimestamp(firebaseCharacter.createdAt),
            updatedAt: convertTimestamp(firebaseCharacter.updatedAt)
          }
        })

        // Fetch plot points for this book
        const plotPointsQuery = query(
          collection(db, COLLECTIONS.BOOKS, book.id, 'plotPoints'),
          orderBy('orderIndex', 'asc')
        )
        const plotPointsSnapshot = await getDocs(plotPointsQuery)

        book.plotPoints = plotPointsSnapshot.docs.map(plotPointDoc => {
          const firebasePlotPoint = { id: plotPointDoc.id, ...plotPointDoc.data() } as FirebasePlotPoint & { id: string }
          return {
            id: firebasePlotPoint.id,
            bookId: firebasePlotPoint.bookId,
            title: firebasePlotPoint.title,
            description: firebasePlotPoint.description || '',
            chapterId: firebasePlotPoint.chapterId,
            sceneId: firebasePlotPoint.sceneId,
            completed: firebasePlotPoint.completed,
            order: firebasePlotPoint.orderIndex,
            orderIndex: firebasePlotPoint.orderIndex,
            createdAt: convertTimestamp(firebasePlotPoint.createdAt),
            updatedAt: convertTimestamp(firebasePlotPoint.updatedAt)
          }
        })

        booksData.push(book)
      }

      setBooks(booksData)
      setError(null)
    } catch (err) {
      console.error('Error fetching books:', err)
      setError('Failed to fetch books')
    } finally {
      setLoading(false)
    }
  }

  const createBook = async (title: string, author: string) => {
    if (!userId) return null

    try {
      const bookData: Omit<FirebaseBook, 'id'> = {
        userId,
        title,
        author,
        wordCount: 0,
        settings: {
          fontSize: 16,
          fontFamily: 'serif',
          theme: 'light',
          lineHeight: 1.6,
          paragraphSpacing: 1.2,
        },
        createdAt: serverTimestamp() as any,
        updatedAt: serverTimestamp() as any
      }

      const docRef = await addDoc(collection(db, COLLECTIONS.BOOKS), bookData)

      const newBook: Book = {
        id: docRef.id,
        title,
        author,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        wordCount: 0,
        settings: bookData.settings,
        chapters: [],
        characters: [],
        plotPoints: []
      }

      setBooks(prev => [newBook, ...prev])
      return newBook
    } catch (err) {
      console.error('Error creating book:', err)
      throw err
    }
  }

  const updateBook = async (book: Book) => {
    try {
      // Update book metadata
      const bookRef = doc(db, COLLECTIONS.BOOKS, book.id)
      await updateDoc(bookRef, {
        title: book.title,
        author: book.author,
        wordCount: book.wordCount,
        settings: book.settings,
        updatedAt: serverTimestamp()
      })

      // Update chapters and scenes
      for (const chapter of book.chapters) {
        const chapterRef = doc(db, COLLECTIONS.BOOKS, book.id, 'chapters', chapter.id)
        await updateDoc(chapterRef, {
          title: chapter.title,
          content: chapter.content,
          wordCount: chapter.wordCount,
          orderIndex: chapter.order,
          updatedAt: serverTimestamp()
        })

        // Update scenes
        for (const scene of chapter.scenes) {
          const sceneRef = doc(db, COLLECTIONS.BOOKS, book.id, 'chapters', chapter.id, 'scenes', scene.id)
          await updateDoc(sceneRef, {
            title: scene.title,
            content: scene.content,
            wordCount: countWords(scene.content),
            orderIndex: scene.order,
            updatedAt: serverTimestamp()
          })
        }
      }

      // Update characters
      for (const character of book.characters) {
        const characterRef = doc(db, COLLECTIONS.BOOKS, book.id, 'characters', character.id)
        await updateDoc(characterRef, {
          name: character.name,
          description: character.description,
          role: character.role,
          appearance: character.appearance,
          personality: character.personality,
          backstory: character.backstory,
          goals: character.goals,
          notes: character.notes,
          updatedAt: serverTimestamp()
        })
      }

      // Update plot points
      for (const plotPoint of book.plotPoints) {
        const plotPointRef = doc(db, COLLECTIONS.BOOKS, book.id, 'plotPoints', plotPoint.id)
        await updateDoc(plotPointRef, {
          title: plotPoint.title,
          description: plotPoint.description,
          chapterId: plotPoint.chapterId,
          sceneId: plotPoint.sceneId,
          completed: plotPoint.completed,
          orderIndex: plotPoint.order,
          updatedAt: serverTimestamp()
        })
      }

      // Update local state
      setBooks(prev => prev.map(b => b.id === book.id ? book : b))
    } catch (err) {
      console.error('Error updating book:', err)
      throw err
    }
  }

  const deleteBook = async (bookId: string) => {
    try {
      // Note: In Firestore, deleting a document doesn't automatically delete subcollections
      // For a complete implementation, you'd need to delete all subcollections first
      // For now, we'll just delete the main book document
      const bookRef = doc(db, COLLECTIONS.BOOKS, bookId)
      await deleteDoc(bookRef)

      setBooks(prev => prev.filter(b => b.id !== bookId))
    } catch (err) {
      console.error('Error deleting book:', err)
      throw err
    }
  }

  return {
    books,
    loading,
    error,
    createBook,
    updateBook,
    deleteBook,
    refetch: fetchBooks
  }
}