import { useState, useEffect } from 'react'
import { supabase } from '../lib/supabase'
import { Book, Chapter, Scene, Character, PlotPoint } from '../types'
import { countWords } from '../utils/bookUtils'

export function useBooks(userId: string | undefined) {
  const [books, setBooks] = useState<Book[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (userId) {
      fetchBooks()
    } else {
      setBooks([])
      setLoading(false)
    }
  }, [userId])

  const fetchBooks = async () => {
    if (!userId) return

    try {
      setLoading(true)
      
      // Fetch books with all related data
      const { data: booksData, error: booksError } = await supabase
        .from('books')
        .select(`
          *,
          chapters (
            *,
            scenes (*)
          ),
          characters (*),
          plot_points (*)
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false })

      if (booksError) throw booksError

      // Transform the data to match our Book interface
      const transformedBooks: Book[] = (booksData || []).map(book => ({
        id: book.id,
        title: book.title,
        author: book.author,
        createdAt: book.created_at,
        updatedAt: book.updated_at,
        wordCount: book.word_count,
        settings: book.settings || {
          fontSize: 16,
          fontFamily: 'serif',
          theme: 'light',
          lineHeight: 1.6,
          paragraphSpacing: 1.2,
        },
        chapters: (book.chapters || [])
          .sort((a: any, b: any) => a.order_index - b.order_index)
          .map((chapter: any) => ({
            id: chapter.id,
            title: chapter.title,
            content: chapter.content || '',
            wordCount: chapter.word_count,
            order: chapter.order_index,
            createdAt: chapter.created_at,
            updatedAt: chapter.updated_at,
            scenes: (chapter.scenes || [])
              .sort((a: any, b: any) => a.order_index - b.order_index)
              .map((scene: any) => ({
                id: scene.id,
                title: scene.title,
                content: scene.content || '',
                wordCount: scene.word_count,
                order: scene.order_index,
                createdAt: scene.created_at,
                updatedAt: scene.updated_at,
              }))
          })),
        characters: (book.characters || []).map((char: any) => ({
          id: char.id,
          name: char.name,
          description: char.description || '',
          role: char.role || '',
          notes: char.notes || '',
          appearance: char.appearance || '',
          personality: char.personality || '',
          backstory: char.backstory || '',
          goals: char.goals || '',
          createdAt: char.created_at,
          updatedAt: char.updated_at,
        })),
        plotPoints: (book.plot_points || [])
          .sort((a: any, b: any) => a.order_index - b.order_index)
          .map((plot: any) => ({
            id: plot.id,
            title: plot.title,
            description: plot.description || '',
            chapterId: plot.chapter_id,
            sceneId: plot.scene_id,
            completed: plot.completed,
            order: plot.order_index,
            createdAt: plot.created_at,
            updatedAt: plot.updated_at,
          }))
      }))

      setBooks(transformedBooks)
    } catch (err) {
      console.error('Error fetching books:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch books')
    } finally {
      setLoading(false)
    }
  }

  const createBook = async (title: string, author: string) => {
    if (!userId) return null

    try {
      const { data, error } = await supabase
        .from('books')
        .insert({
          user_id: userId,
          title,
          author,
          word_count: 0,
          settings: {
            fontSize: 16,
            fontFamily: 'serif',
            theme: 'light',
            lineHeight: 1.6,
            paragraphSpacing: 1.2,
          }
        })
        .select()
        .single()

      if (error) throw error

      const newBook: Book = {
        id: data.id,
        title: data.title,
        author: data.author,
        createdAt: data.created_at,
        updatedAt: data.updated_at,
        wordCount: data.word_count,
        settings: data.settings,
        chapters: [],
        characters: [],
        plotPoints: []
      }

      setBooks(prev => [newBook, ...prev])
      return newBook
    } catch (err) {
      console.error('Error creating book:', err)
      throw err
    }
  }

  const updateBook = async (book: Book) => {
    try {
      // Update book metadata
      const { error: bookError } = await supabase
        .from('books')
        .update({
          title: book.title,
          author: book.author,
          word_count: book.wordCount,
          settings: book.settings,
          updated_at: new Date().toISOString()
        })
        .eq('id', book.id)

      if (bookError) throw bookError

      // Update chapters and scenes
      for (const chapter of book.chapters) {
        const { error: chapterError } = await supabase
          .from('chapters')
          .upsert({
            id: chapter.id,
            book_id: book.id,
            title: chapter.title,
            content: chapter.content,
            word_count: chapter.wordCount,
            order_index: chapter.order,
            updated_at: new Date().toISOString()
          })

        if (chapterError) throw chapterError

        // Update scenes
        for (const scene of chapter.scenes) {
          const { error: sceneError } = await supabase
            .from('scenes')
            .upsert({
              id: scene.id,
              chapter_id: chapter.id,
              title: scene.title,
              content: scene.content,
              word_count: countWords(scene.content),
              order_index: scene.order,
              updated_at: new Date().toISOString()
            })

          if (sceneError) throw sceneError
        }
      }

      // Update characters
      for (const character of book.characters) {
        const { error: charError } = await supabase
          .from('characters')
          .upsert({
            id: character.id,
            book_id: book.id,
            name: character.name,
            description: character.description,
            role: character.role,
            appearance: character.appearance,
            personality: character.personality,
            backstory: character.backstory,
            goals: character.goals,
            notes: character.notes,
            updated_at: new Date().toISOString()
          })

        if (charError) throw charError
      }

      // Update plot points
      for (const plotPoint of book.plotPoints) {
        const { error: plotError } = await supabase
          .from('plot_points')
          .upsert({
            id: plotPoint.id,
            book_id: book.id,
            title: plotPoint.title,
            description: plotPoint.description,
            chapter_id: plotPoint.chapterId,
            scene_id: plotPoint.sceneId,
            completed: plotPoint.completed,
            order_index: plotPoint.order,
            updated_at: new Date().toISOString()
          })

        if (plotError) throw plotError
      }

      // Update local state
      setBooks(prev => prev.map(b => b.id === book.id ? book : b))
    } catch (err) {
      console.error('Error updating book:', err)
      throw err
    }
  }

  const deleteBook = async (bookId: string) => {
    try {
      const { error } = await supabase
        .from('books')
        .delete()
        .eq('id', bookId)

      if (error) throw error

      setBooks(prev => prev.filter(b => b.id !== bookId))
    } catch (err) {
      console.error('Error deleting book:', err)
      throw err
    }
  }

  return {
    books,
    loading,
    error,
    createBook,
    updateBook,
    deleteBook,
    refetch: fetchBooks
  }
}