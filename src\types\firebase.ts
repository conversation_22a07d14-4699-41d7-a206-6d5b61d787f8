import { Timestamp } from 'firebase/firestore'

// User Profile
export interface Profile {
  id: string
  email: string
  fullName: string | null
  avatarUrl: string | null
  onboardingCompleted: boolean
  writingGoals: string[]
  experience: string
  genres: string[]
  dailyGoal: number
  createdAt: Timestamp
  updatedAt: Timestamp
}

// Book Settings
export interface BookSettings {
  fontSize: number
  fontFamily: string
  theme: string
  lineHeight: number
  paragraphSpacing: number
}

// Book
export interface Book {
  id: string
  userId: string
  title: string
  author: string
  wordCount: number
  settings: BookSettings
  createdAt: Timestamp
  updatedAt: Timestamp
}

// Chapter
export interface Chapter {
  id: string
  bookId: string
  title: string
  content: string
  wordCount: number
  orderIndex: number
  createdAt: Timestamp
  updatedAt: Timestamp
}

// Scene
export interface Scene {
  id: string
  chapterId: string
  bookId: string // Denormalized for easier querying
  title: string
  content: string
  wordCount: number
  orderIndex: number
  createdAt: Timestamp
  updatedAt: Timestamp
}

// Character
export interface Character {
  id: string
  bookId: string
  name: string
  description: string | null
  role: string | null
  appearance: string | null
  personality: string | null
  backstory: string | null
  goals: string | null
  notes: string | null
  createdAt: Timestamp
  updatedAt: Timestamp
}

// Plot Point
export interface PlotPoint {
  id: string
  bookId: string
  title: string
  description: string | null
  chapterId: string | null
  sceneId: string | null
  completed: boolean
  orderIndex: number
  createdAt: Timestamp
  updatedAt: Timestamp
}

// Firestore Collection Names
export const COLLECTIONS = {
  USERS: 'users',
  BOOKS: 'books',
  CHAPTERS: 'chapters',
  SCENES: 'scenes',
  CHARACTERS: 'characters',
  PLOT_POINTS: 'plotPoints'
} as const

/*
Firestore Data Structure:

/users/{userId} - Profile document
/books/{bookId} - Book document
/books/{bookId}/chapters/{chapterId} - Chapter subcollection
/books/{bookId}/chapters/{chapterId}/scenes/{sceneId} - Scene subcollection
/books/{bookId}/characters/{characterId} - Character subcollection
/books/{bookId}/plotPoints/{plotPointId} - Plot point subcollection

This structure allows for:
1. Easy user data isolation
2. Efficient book-specific queries
3. Hierarchical organization of chapters and scenes
4. Good performance for common operations
*/
