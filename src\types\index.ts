export interface Book {
  id: string;
  title: string;
  author: string;
  createdAt: string;
  updatedAt: string;
  wordCount: number;
  chapters: Chapter[];
  characters: Character[];
  plotPoints: PlotPoint[];
  settings: BookSettings;
}

export interface Chapter {
  id: string;
  title: string;
  content: string;
  wordCount: number;
  scenes: Scene[];
  order: number;
  createdAt: string;
  updatedAt: string;
}

export interface Scene {
  id: string;
  title: string;
  content: string;
  wordCount: number;
  order: number;
  createdAt: string;
  updatedAt: string;
}

export interface Character {
  id: string;
  name: string;
  description: string;
  role: string;
  notes: string;
  appearance: string;
  personality: string;
  backstory: string;
  goals: string;
  createdAt: string;
  updatedAt: string;
}

export interface PlotPoint {
  id: string;
  title: string;
  description: string;
  chapterId?: string;
  sceneId?: string;
  completed: boolean;
  order: number;
  createdAt: string;
  updatedAt: string;
}

export interface BookSettings {
  backgroundImage?: string;
  fontSize: number;
  fontFamily: 'serif' | 'sans';
  theme: 'light' | 'dark';
  lineHeight: number;
  paragraphSpacing: number;
}

export interface WritingSession {
  id: string;
  bookId: string;
  startTime: string;
  endTime?: string;
  wordsWritten: number;
  targetWords?: number;
}

export type ViewMode = 'writing' | 'characters' | 'plot' | 'settings' | 'statistics';</content>