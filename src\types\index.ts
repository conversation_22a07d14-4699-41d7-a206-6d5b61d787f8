export interface Book {
  id: string;
  title: string;
  author: string;
  createdAt: string;
  updatedAt: string;
  wordCount: number;
  chapters: Chapter[];
  characters: Character[];
  plotPoints: PlotPoint[];
  settings: BookSettings;
}

export interface Chapter {
  id: string;
  bookId: string;
  title: string;
  content: string;
  wordCount: number;
  scenes: Scene[];
  order: number; // Keep as 'order' for compatibility, map to 'orderIndex' in Firebase
  orderIndex: number; // Firebase field name
  createdAt: string;
  updatedAt: string;
}

export interface Scene {
  id: string;
  chapterId: string;
  title: string;
  content: string;
  wordCount: number;
  order: number; // Keep as 'order' for compatibility, map to 'orderIndex' in Firebase
  orderIndex: number; // Firebase field name
  createdAt: string;
  updatedAt: string;
}

export interface Character {
  id: string;
  bookId: string;
  name: string;
  description: string | null;
  role: string | null;
  notes: string | null;
  appearance: string | null;
  personality: string | null;
  backstory: string | null;
  goals: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface PlotPoint {
  id: string;
  title: string;
  description: string;
  chapterId?: string;
  sceneId?: string;
  completed: boolean;
  order: number;
  createdAt: string;
  updatedAt: string;
}

export interface BookSettings {
  backgroundImage?: string;
  fontSize: number;
  fontFamily: 'serif' | 'sans';
  theme: 'light' | 'dark';
  lineHeight: number;
  paragraphSpacing: number;
}

export interface WritingSession {
  id: string;
  bookId: string;
  startTime: string;
  endTime?: string;
  wordsWritten: number;
  targetWords?: number;
}

export type ViewMode = 'writing' | 'characters' | 'plot' | 'settings' | 'statistics';</content>