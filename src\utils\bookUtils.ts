import { v4 as uuidv4 } from 'uuid';
import { Book, Chapter, Scene, Character, PlotPoint, WritingSession } from '../types';

export const createNewBook = (title: string, author: string): Book => {
  const now = new Date().toISOString();
  return {
    id: generateId(),
    title,
    author,
    createdAt: now,
    updatedAt: now,
    wordCount: 0,
    chapters: [],
    characters: [],
    plotPoints: [],
    settings: {
      fontSize: 16,
      fontFamily: 'serif',
      theme: 'light',
      lineHeight: 1.6,
      paragraphSpacing: 1.2,
    },
  };
};

export const createNewChapter = (title: string, order: number): Chapter => {
  const now = new Date().toISOString();
  return {
    id: generateId(),
    title,
    content: '',
    wordCount: 0,
    scenes: [],
    order,
    createdAt: now,
    updatedAt: now,
  };
};

export const createNewScene = (title: string, order: number): Scene => {
  const now = new Date().toISOString();
  return {
    id: generateId(),
    title,
    content: '',
    wordCount: 0,
    order,
    createdAt: now,
    updatedAt: now,
  };
};

export const createNewCharacter = (name: string): Character => {
  const now = new Date().toISOString();
  return {
    id: generateId(),
    name,
    description: '',
    role: '',
    notes: '',
    appearance: '',
    personality: '',
    backstory: '',
    goals: '',
    createdAt: now,
    updatedAt: now,
  };
};

export const createNewPlotPoint = (title: string, order: number): PlotPoint => {
  const now = new Date().toISOString();
  return {
    id: generateId(),
    title,
    description: '',
    completed: false,
    order,
    createdAt: now,
    updatedAt: now,
  };
};

export const generateId = (): string => {
  return uuidv4();
};

export const countWords = (text: string): number => {
  if (!text || text.trim() === '') return 0;
  // Remove HTML tags and count words
  const plainText = text.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim();
  return plainText.split(' ').filter(word => word.length > 0).length;
};

export const updateBookWordCount = (book: Book): Book => {
  const totalWords = book.chapters.reduce((total, chapter) => {
    const chapterWords = chapter.scenes.reduce((sceneTotal, scene) => {
      return sceneTotal + countWords(scene.content);
    }, countWords(chapter.content));
    return total + chapterWords;
  }, 0);

  return {
    ...book,
    wordCount: totalWords,
    updatedAt: new Date().toISOString(),
  };
};

export const deleteChapter = (book: Book, chapterId: string): Book => {
  return {
    ...book,
    chapters: book.chapters.filter(chapter => chapter.id !== chapterId),
    updatedAt: new Date().toISOString(),
  };
};

export const deleteScene = (book: Book, chapterId: string, sceneId: string): Book => {
  return {
    ...book,
    chapters: book.chapters.map(chapter => 
      chapter.id === chapterId 
        ? { ...chapter, scenes: chapter.scenes.filter(scene => scene.id !== sceneId) }
        : chapter
    ),
    updatedAt: new Date().toISOString(),
  };
};

export const deleteCharacter = (book: Book, characterId: string): Book => {
  return {
    ...book,
    characters: book.characters.filter(character => character.id !== characterId),
    updatedAt: new Date().toISOString(),
  };
};

export const deletePlotPoint = (book: Book, plotPointId: string): Book => {
  return {
    ...book,
    plotPoints: book.plotPoints.filter(plotPoint => plotPoint.id !== plotPointId),
    updatedAt: new Date().toISOString(),
  };
};

export const reorderChapters = (book: Book, startIndex: number, endIndex: number): Book => {
  const chapters = Array.from(book.chapters);
  const [reorderedItem] = chapters.splice(startIndex, 1);
  chapters.splice(endIndex, 0, reorderedItem);
  
  // Update order numbers
  const updatedChapters = chapters.map((chapter, index) => ({
    ...chapter,
    order: index,
    updatedAt: new Date().toISOString(),
  }));

  return {
    ...book,
    chapters: updatedChapters,
    updatedAt: new Date().toISOString(),
  };
};

export const reorderScenes = (book: Book, chapterId: string, startIndex: number, endIndex: number): Book => {
  return {
    ...book,
    chapters: book.chapters.map(chapter => {
      if (chapter.id !== chapterId) return chapter;
      
      const scenes = Array.from(chapter.scenes);
      const [reorderedItem] = scenes.splice(startIndex, 1);
      scenes.splice(endIndex, 0, reorderedItem);
      
      // Update order numbers
      const updatedScenes = scenes.map((scene, index) => ({
        ...scene,
        order: index,
        updatedAt: new Date().toISOString(),
      }));

      return {
        ...chapter,
        scenes: updatedScenes,
        updatedAt: new Date().toISOString(),
      };
    }),
    updatedAt: new Date().toISOString(),
  };
};

export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

export const getWritingStatistics = (book: Book): {
  totalWords: number;
  totalChapters: number;
  totalScenes: number;
  averageWordsPerChapter: number;
  averageWordsPerScene: number;
  lastUpdated: string;
} => {
  const totalChapters = book.chapters.length;
  const totalScenes = book.chapters.reduce((total, chapter) => total + chapter.scenes.length, 0);
  const totalWords = book.wordCount;
  
  return {
    totalWords,
    totalChapters,
    totalScenes,
    averageWordsPerChapter: totalChapters > 0 ? Math.round(totalWords / totalChapters) : 0,
    averageWordsPerScene: totalScenes > 0 ? Math.round(totalWords / totalScenes) : 0,
    lastUpdated: book.updatedAt,
  };
};