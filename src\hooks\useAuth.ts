import { useState, useEffect } from 'react'
import {
  User,
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut as firebaseSignOut,
  onAuthStateChanged,
  updateProfile
} from 'firebase/auth'
import {
  doc,
  getDoc,
  setDoc,
  updateDoc,
  serverTimestamp
} from 'firebase/firestore'
import { auth, db } from '../lib/firebase'
import { Profile, COLLECTIONS } from '../types/firebase'

// Profile interface is now imported from types/firebase.ts

export function useAuth() {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<Profile | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      console.log('🔐 Auth state changed:', user ? `User ${user.uid}` : 'No user')
      setUser(user)
      if (user) {
        console.log('👤 Fetching profile for user:', user.uid)
        await fetchProfile(user.uid)
      } else {
        setProfile(null)
        setLoading(false)
      }
    })

    return () => unsubscribe()
  }, [])

  const fetchProfile = async (userId: string) => {
    try {
      console.log('📄 Fetching profile document for:', userId)
      const userDocRef = doc(db, COLLECTIONS.USERS, userId)
      const userDoc = await getDoc(userDocRef)

      if (!userDoc.exists()) {
        console.log('📝 Profile doesn\'t exist, creating new profile')
        // Profile doesn't exist, create one
        const userEmail = auth.currentUser?.email || ''
        const newProfile: Omit<Profile, 'id'> = {
          email: userEmail,
          fullName: auth.currentUser?.displayName || null,
          avatarUrl: auth.currentUser?.photoURL || null,
          onboardingCompleted: true, // Skip onboarding, go straight to writing
          writingGoals: ['Write every day'],
          experience: 'beginner',
          genres: ['Fiction'],
          dailyGoal: 500,
          createdAt: serverTimestamp() as any,
          updatedAt: serverTimestamp() as any
        }

        await setDoc(userDocRef, newProfile)
        console.log('✅ Profile created successfully')
        setProfile({ id: userId, ...newProfile } as Profile)
      } else {
        console.log('📖 Profile found, loading existing profile')
        const profileData = userDoc.data() as Omit<Profile, 'id'>
        setProfile({ id: userId, ...profileData })
      }
    } catch (error) {
      console.error('❌ Error fetching profile:', error)
    } finally {
      console.log('🏁 Setting loading to false')
      setLoading(false)
    }
  }

  const signUp = async (email: string, password: string, fullName: string) => {
    try {
      const userCredential = await createUserWithEmailAndPassword(auth, email, password)

      // Update the user's display name
      await updateProfile(userCredential.user, {
        displayName: fullName
      })

      return { data: userCredential, error: null }
    } catch (error) {
      return { data: null, error }
    }
  }

  const signIn = async (email: string, password: string) => {
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password)
      return { data: userCredential, error: null }
    } catch (error) {
      return { data: null, error }
    }
  }

  const signOut = async () => {
    try {
      await firebaseSignOut(auth)
      return { error: null }
    } catch (error) {
      return { error }
    }
  }

  const updateProfile = async (updates: Partial<Profile>) => {
    if (!user) return { error: new Error('No user') }

    try {
      const userDocRef = doc(db, COLLECTIONS.USERS, user.uid)
      const updateData = {
        ...updates,
        updatedAt: serverTimestamp()
      }

      await updateDoc(userDocRef, updateData)

      // Update local state
      if (profile) {
        setProfile({ ...profile, ...updates } as Profile)
      }

      return { data: updates, error: null }
    } catch (error) {
      return { data: null, error }
    }
  }



  return {
    user,
    profile,
    loading,
    signUp,
    signIn,
    signOut,
    updateProfile,
  }
}