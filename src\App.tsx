import React from 'react';
import { useState, useEffect } from 'react';
import { Book, Chapter, Scene } from './types';
import { createNewBook, createNewChapter, createNewScene } from './utils/bookUtils';
import { WritingInterface } from './components/WritingInterface';
import { AuthForm } from './components/AuthForm';
import { OnboardingFlow, OnboardingData } from './components/OnboardingFlow';
import { useAuth } from './hooks/useAuth';
import { useBooks } from './hooks/useBooks';

function App() {
  const { user, profile, loading: authLoading, signIn, signUp, signOut, completeOnboarding, saveOnboardingData } = useAuth();
  const { books, loading: booksLoading, createBook, updateBook, deleteBook } = useBooks(user?.uid);
  const [currentBook, setCurrentBook] = useState<Book | null>(() => {
    return null;
  });

  // Auto-select first book when books load
  useEffect(() => {
    if (books.length > 0 && !currentBook) {
      setCurrentBook(books[0]);
    }
  }, [books, currentBook]);

  const handleCreateBook = async (title: string, author: string) => {
    try {
      const newBook = await createBook(title, author);
      if (newBook) {
        setCurrentBook(newBook);
      }
    } catch (error) {
      console.error('Error creating book:', error);
    }
  };

  const handleUpdateBook = async (updatedBook: Book) => {
    try {
      await updateBook(updatedBook);
      setCurrentBook(updatedBook);
    } catch (error) {
      console.error('Error updating book:', error);
    }
  };

  const handleSelectBook = (book: Book) => {
    setCurrentBook(book);
  };

  const handleDeleteBook = async (bookId: string) => {
    try {
      await deleteBook(bookId);
      
      // If we deleted the current book, select another one
      if (currentBook?.id === bookId) {
        const remainingBooks = books.filter(book => book.id !== bookId);
        if (remainingBooks.length > 0) {
          setCurrentBook(remainingBooks[0]);
        } else {
          setCurrentBook(null);
        }
      }
    } catch (error) {
      console.error('Error deleting book:', error);
    }
  };

  const handleOnboardingComplete = async (data: OnboardingData) => {
    try {
      // Save onboarding data and complete onboarding
      await completeOnboarding();
      await saveOnboardingData({
        writingGoals: data.writingGoals,
        experience: data.experience,
        genres: data.genres,
        dailyGoal: data.dailyGoal
      });
      
      // Create first book with a default chapter and scene
      const newBook = await createBook(data.firstBookTitle, data.firstBookAuthor);
      if (newBook) {
        // Create a default chapter
        const chapter = createNewChapter('Chapter 1', 0);
        const scene = createNewScene('Opening', 0);
        chapter.scenes = [scene];
        
        const bookWithChapter = {
          ...newBook,
          chapters: [chapter]
        };
        
        await updateBook(bookWithChapter);
        setCurrentBook(bookWithChapter);
      }
    } catch (error) {
      console.error('Error completing onboarding:', error);
    }
  };

  const handleSignOut = async () => {
    try {
      await signOut();
      setCurrentBook(null);
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  // Show loading screen
  if (authLoading) {
    return (
      <div className="min-h-screen bg-blue-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-serif text-blue-gray-700 mb-4">
            Loading...
          </h2>
        </div>
      </div>
    );
  }

  // Show auth form if not authenticated
  if (!user) {
    return (
      <AuthForm
        onSignIn={signIn}
        onSignUp={signUp}
        loading={authLoading}
      />
    );
  }

  // Show onboarding if not completed
  if (profile && !profile.onboarding_completed) {
    return (
      <OnboardingFlow
        onComplete={handleOnboardingComplete}
        userFullName={profile.full_name || ''}
      />
    );
  }

  // Show loading while books are loading
  if (booksLoading) {
    return (
      <div className="min-h-screen bg-blue-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-serif text-blue-gray-700 mb-4">
            Loading your books...
          </h2>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      {currentBook || books.length > 0 ? (
        <WritingInterface
          book={currentBook || books[0]}
          books={books}
          onUpdateBook={handleUpdateBook}
          onCreateBook={handleCreateBook}
          onSelectBook={handleSelectBook}
          onDeleteBook={handleDeleteBook}
          onSignOut={handleSignOut}
          user={user}
        />
      ) : (
        <div className="min-h-screen bg-blue-gray-50 flex items-center justify-center p-8">
          <div className="text-center">
            <h2 className="text-3xl font-serif text-blue-gray-700 mb-4">
              Welcome to WriterOne!
            </h2>
            <p className="text-blue-gray-600 font-sans mb-8">
              You don't have any books yet. Create your first book to get started.
            </p>
            <button
              onClick={() => handleCreateBook('My First Novel', profile?.full_name || 'Your Name')}
              className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-sans font-medium rounded-lg transition-colors"
            >
              Create Your First Book
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

export default App;